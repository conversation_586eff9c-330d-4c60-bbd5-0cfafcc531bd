from pydantic import BaseModel
from typing import List
import logging

import litserve as ls
from litserve.mcp import MCP

# https://github.com/deedy5/duckduckgo_search
from ddgs import DDGS

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),  # Console output
        #logging.FileHandler('mcp-server.log')  # File output
    ]
)
logger = logging.getLogger(__name__)


class AddRequest(BaseModel):
    a: int
    b: int


class TextRequest(BaseModel):
    text: str


class AddAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: AddRequest, **kwargs):
        return request

    def predict(self, x: AddRequest, *args, **kwargs):
        return {"result": x.a + x.b}


class ReverseAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"reversed": x.text[::-1]}


class UpperAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request: TextRequest, **kwargs):
        return request

    def predict(self, x: TextRequest, **kwargs):
        return {"upper": x.text.upper()}


class WebSearchResult(BaseModel):
    url: str
    title: str
    description: str | None = None


class WebSearchResponse(BaseModel):
    results: List[WebSearchResult]


class DuckDuckGoSearchAPI(ls.LitAPI):
    def setup(self, device):
        pass

    def decode_request(self, request, **kwargs):
        logger.info("Received request: %s", request)
        logger.info("Request type: %s", type(request))
        logger.info("Kwargs: %s", kwargs)

        return request

    def predict(self, x: TextRequest, **kwargs):
        logger.info("Searching for: %s", x.text)

        search_results = []

        try:
            with DDGS() as ddgs:
                for result in ddgs.text(
                    query=x.text, region="fr-fr", max_results=10, backend="lite"
                ):
                    search_results.append(
                        WebSearchResult(
                            url=result.get("href", ""),
                            title=result.get("title", ""),
                            description=result.get("body", ""),
                        )
                    )
            logger.info("Found %d results", len(search_results))
        except (RuntimeError, ValueError) as e:
            logger.error("Error during search: %s", e)
            search_results = []

        return WebSearchResponse(results=search_results)


if __name__ == "__main__":
    logger.info("Starting MCP Server...")

    add_mcp = MCP(
        name="add_numbers", description="Ajoute deux nombres et retourne le résultat."
    )
    reverse_mcp = MCP(
        name="reverse_string", description="Inverse une chaîne de caractères donnée."
    )
    upper_mcp = MCP(
        name="uppercase_string",
        description="Convertit une chaîne de caractères donnée en majuscules.",
    )

    duckduckgo_mcp = MCP(
        name="duckduckgo_search",
        description="Effectue une recherche sur DuckDuckGo et retourne les résultats.",
    )
    """input_schema={
            "type": "object",
            "properties": {
                "text": {"type": "string", "description": "Texte de la recherche"}
            },
            "required": ["text"],
        },"""

    logger.info("Configuring MCP server...")
    server = ls.LitServer(
        [
            # AddAPI(mcp=add_mcp, api_path="/add"),
            # ReverseAPI(mcp=reverse_mcp, api_path="/reverse"),
            # UpperAPI(mcp=upper_mcp, api_path="/upper"),
            DuckDuckGoSearchAPI(mcp=duckduckgo_mcp, api_path="/websearch"),
        ],
    )

    # lsof -ti:8002 | xargs kill -9
    logger.info("Starting server on port 8002...")
    server.run(port=8002)
